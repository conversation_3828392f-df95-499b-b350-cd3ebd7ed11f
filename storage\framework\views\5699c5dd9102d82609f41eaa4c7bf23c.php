<?php $__env->startSection('title', 'पारिवारिक वंशावली'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-navy-900 dark:via-navy-800 dark:to-navy-900">
    <div class="px-4 sm:px-6 lg:px-8 py-6">
        <!-- Enhanced <PERSON>er -->
        <div class="mb-8 no-print">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="p-3 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-lg">
                        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                            पारिवारिक वंशावली
                        </h1>
                        <p class="mt-1 text-gray-600 dark:text-gray-400 text-lg">
                            <?php echo e($member->name); ?> का पूर्वजों का विवरण
                        </p>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button onclick="toggleTreeView()" class="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <span id="viewToggleText">🌳 ट्री व्यू</span>
                    </button>
                    <button onclick="window.print()" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        📄 प्रिंट करें
                    </button>
                    <a href="<?php echo e(route('member.dashboard')); ?>" class="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        ← वापस
                    </a>
                </div>
            </div>
        </div>

        <?php if($member->ancestors && count($member->ancestors) > 0): ?>
            <?php
                // Sort ancestors by generation level (highest to lowest)
                $sortedAncestors = collect($member->ancestors)->sortByDesc('generation_level');
                $generationLabels = [
                    7 => '7वीं पीढ़ी',
                    6 => '6वीं पीढ़ी',
                    5 => '5वीं पीढ़ी',
                    4 => '4वीं पीढ़ी',
                    3 => '3वीं पीढ़ी',
                    2 => '2वीं पीढ़ी (दादा-दादी)',
                    1 => '1वीं पीढ़ी (माता-पिता)'
                ];
            ?>

            <!-- Tree View Container -->
            <div id="treeView" class="family-tree-container">
                <div class="bg-white/80 dark:bg-navy-800/80 backdrop-blur-sm border border-white/20 dark:border-navy-700/50 rounded-2xl shadow-2xl overflow-hidden">
                    <div class="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-6 text-center">
                        <h2 class="text-2xl font-bold text-white mb-2">
                            🌳 पारिवारिक वंशावली चार्ट
                        </h2>
                        <p class="text-blue-100"><?php echo e($member->name); ?> का पारिवारिक वृक्ष</p>
                    </div>

                    <div class="p-8 overflow-x-auto">
                        <div class="family-tree-wrapper min-w-max">
                            <!-- Tree Structure -->
                            <div class="tree-container relative">
                                <?php $__currentLoopData = $generationLabels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $ancestorsInLevel = $sortedAncestors->where('generation_level', $level);
                                    ?>
                                    <?php if($ancestorsInLevel->count() > 0): ?>
                                        <div class="generation-level mb-16 relative" data-level="<?php echo e($level); ?>">
                                            <!-- Generation Label -->
                                            <div class="text-center mb-8">
                                                <div class="inline-block bg-gradient-to-r from-indigo-500 to-purple-500 text-white px-6 py-3 rounded-full shadow-lg font-bold text-lg">
                                                    <?php echo e($label); ?>

                                                </div>
                                            </div>

                                            <!-- Ancestors in this level -->
                                            <div class="flex justify-center items-center space-x-8 flex-wrap">
                                                <?php $__currentLoopData = $ancestorsInLevel; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $ancestor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="ancestor-node relative group">
                                                        <!-- Connection Lines -->
                                                        <?php if($level > 1): ?>
                                                            <div class="connection-line absolute -top-16 left-1/2 transform -translate-x-1/2 w-0.5 h-16 bg-gradient-to-b from-indigo-400 to-purple-400"></div>
                                                        <?php endif; ?>

                                                        <!-- Ancestor Card -->
                                                        <div class="ancestor-card bg-gradient-to-br from-white to-blue-50 dark:from-navy-700 dark:to-navy-600 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-blue-200 dark:border-navy-500 p-6 min-w-[280px] max-w-[320px]">
                                                            <!-- Avatar -->
                                                            <div class="text-center mb-4">
                                                                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto shadow-lg">
                                                                    <span class="text-2xl text-white font-bold">
                                                                        <?php echo e(substr($ancestor['name'] ?? 'N', 0, 1)); ?>

                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <!-- Name -->
                                                            <h4 class="font-bold text-xl text-gray-900 dark:text-white mb-3 text-center">
                                                                <?php echo e($ancestor['name'] ?? 'अज्ञात'); ?>

                                                            </h4>

                                                            <!-- Details -->
                                                            <div class="space-y-2 text-sm">
                                                                <?php if(!empty($ancestor['birth_date'])): ?>
                                                                    <div class="flex items-center space-x-2">
                                                                        <span class="text-green-500">🎂</span>
                                                                        <span class="text-gray-600 dark:text-gray-300"><?php echo e($ancestor['birth_date']); ?></span>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <?php if(!empty($ancestor['death_date'])): ?>
                                                                    <div class="flex items-center space-x-2">
                                                                        <span class="text-red-500">🕊️</span>
                                                                        <span class="text-gray-600 dark:text-gray-300"><?php echo e($ancestor['death_date']); ?></span>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <?php if(!empty($ancestor['birth_place'])): ?>
                                                                    <div class="flex items-center space-x-2">
                                                                        <span class="text-blue-500">📍</span>
                                                                        <span class="text-gray-600 dark:text-gray-300"><?php echo e($ancestor['birth_place']); ?></span>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <?php if(!empty($ancestor['occupation'])): ?>
                                                                    <div class="flex items-center space-x-2">
                                                                        <span class="text-purple-500">💼</span>
                                                                        <span class="text-gray-600 dark:text-gray-300"><?php echo e($ancestor['occupation']); ?></span>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <?php if(!empty($ancestor['spouse'])): ?>
                                                                    <div class="flex items-center space-x-2">
                                                                        <span class="text-pink-500">💑</span>
                                                                        <span class="text-gray-600 dark:text-gray-300"><?php echo e($ancestor['spouse']); ?></span>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <?php if(!empty($ancestor['children_count'])): ?>
                                                                    <div class="flex items-center space-x-2">
                                                                        <span class="text-orange-500">👶</span>
                                                                        <span class="text-gray-600 dark:text-gray-300"><?php echo e($ancestor['children_count']); ?> संतान</span>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <?php if(!empty($ancestor['gotra'])): ?>
                                                                    <div class="flex items-center space-x-2">
                                                                        <span class="text-indigo-500">🏛️</span>
                                                                        <span class="text-gray-600 dark:text-gray-300"><?php echo e($ancestor['gotra']); ?></span>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                <!-- Current Member (Special Highlight) -->
                                <div class="generation-level mb-16 relative">
                                    <div class="text-center mb-8">
                                        <div class="inline-block bg-gradient-to-r from-green-500 to-emerald-500 text-white px-8 py-4 rounded-full shadow-xl font-bold text-xl animate-pulse">
                                            🌟 वर्तमान सदस्य 🌟
                                        </div>
                                    </div>

                                    <div class="flex justify-center">
                                        <div class="current-member-card bg-gradient-to-br from-green-50 to-emerald-50 dark:from-emerald-900/30 dark:to-green-900/30 rounded-3xl shadow-2xl border-4 border-green-300 dark:border-emerald-600 p-8 min-w-[350px] max-w-[400px] transform hover:scale-105 transition-all duration-300">
                                            <!-- Crown Icon -->
                                            <div class="text-center mb-6">
                                                <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto shadow-xl border-4 border-white">
                                                    <span class="text-3xl text-white font-bold">
                                                        <?php echo e(substr($member->name, 0, 1)); ?>

                                                    </span>
                                                </div>
                                                <div class="text-4xl mt-2">👑</div>
                                            </div>

                                            <!-- Member Name -->
                                            <h4 class="font-bold text-2xl text-green-900 dark:text-green-100 mb-4 text-center">
                                                <?php echo e($member->name); ?>

                                            </h4>

                                            <!-- Member Details -->
                                            <div class="space-y-3 text-sm">
                                                <div class="flex items-center space-x-3">
                                                    <span class="text-blue-500 text-lg">👨‍👦</span>
                                                    <span class="text-gray-700 dark:text-gray-300"><strong>पिता:</strong> <?php echo e($member->fathers_husband_name); ?></span>
                                                </div>
                                                <?php if($member->birth_date): ?>
                                                    <div class="flex items-center space-x-3">
                                                        <span class="text-green-500 text-lg">🎂</span>
                                                        <span class="text-gray-700 dark:text-gray-300"><strong>जन्म:</strong> <?php echo e($member->birth_date->format('d/m/Y')); ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if($member->address): ?>
                                                    <div class="flex items-center space-x-3">
                                                        <span class="text-red-500 text-lg">🏠</span>
                                                        <span class="text-gray-700 dark:text-gray-300"><strong>पता:</strong> <?php echo e(Str::limit($member->address, 40)); ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if($member->department_name): ?>
                                                    <div class="flex items-center space-x-3">
                                                        <span class="text-purple-500 text-lg">🏢</span>
                                                        <span class="text-gray-700 dark:text-gray-300"><strong>विभाग:</strong> <?php echo e($member->department_name); ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="flex items-center space-x-3">
                                                    <span class="text-indigo-500 text-lg">🆔</span>
                                                    <span class="text-gray-700 dark:text-gray-300"><strong>सदस्यता:</strong> <?php echo e($member->membership_number); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- List View Container (Hidden by default) -->
            <div id="listView" class="hidden">
                <div class="bg-white/80 dark:bg-navy-800/80 backdrop-blur-sm border border-white/20 dark:border-navy-700/50 rounded-2xl shadow-2xl overflow-hidden">
                    <div class="bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 p-6 text-center">
                        <h2 class="text-2xl font-bold text-white mb-2">
                            📋 पारिवारिक वंशावली सूची
                        </h2>
                        <p class="text-purple-100">पीढ़ी के अनुसार व्यवस्थित</p>
                    </div>

                    <div class="p-6">
                        <?php $__currentLoopData = $generationLabels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $ancestorsInLevel = $sortedAncestors->where('generation_level', $level);
                            ?>
                            <?php if($ancestorsInLevel->count() > 0): ?>
                                <div class="mb-8">
                                    <h3 class="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-6 pb-3 border-b-2 border-purple-300">
                                        <?php echo e($label); ?>

                                    </h3>

                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                        <?php $__currentLoopData = $ancestorsInLevel; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ancestor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="bg-gradient-to-br from-white to-purple-50 dark:from-navy-700 dark:to-navy-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-purple-200 dark:border-navy-500 p-5">
                                                <div class="flex items-center space-x-3 mb-3">
                                                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-md">
                                                        <span class="text-lg text-white font-bold">
                                                            <?php echo e(substr($ancestor['name'] ?? 'N', 0, 1)); ?>

                                                        </span>
                                                    </div>
                                                    <h4 class="font-bold text-lg text-gray-900 dark:text-white">
                                                        <?php echo e($ancestor['name'] ?? 'अज्ञात'); ?>

                                                    </h4>
                                                </div>

                                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                                                    <?php if(!empty($ancestor['birth_date'])): ?>
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-green-500">🎂</span>
                                                            <span><?php echo e($ancestor['birth_date']); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['death_date'])): ?>
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-red-500">🕊️</span>
                                                            <span><?php echo e($ancestor['death_date']); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['birth_place'])): ?>
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-blue-500">📍</span>
                                                            <span><?php echo e($ancestor['birth_place']); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['occupation'])): ?>
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-purple-500">💼</span>
                                                            <span><?php echo e($ancestor['occupation']); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['spouse'])): ?>
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-pink-500">💑</span>
                                                            <span><?php echo e($ancestor['spouse']); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['children_count'])): ?>
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-orange-500">👶</span>
                                                            <span><?php echo e($ancestor['children_count']); ?> संतान</span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['gotra'])): ?>
                                                        <div class="flex items-center space-x-2">
                                                            <span class="text-indigo-500">🏛️</span>
                                                            <span><?php echo e($ancestor['gotra']); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <!-- Current Member in List View -->
                        <div class="mt-8 pt-6 border-t-4 border-green-500">
                            <h3 class="text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-6">
                                🌟 वर्तमान सदस्य
                            </h3>
                            <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-emerald-900/30 dark:to-green-900/30 rounded-xl shadow-lg border-2 border-green-300 dark:border-emerald-600 p-6">
                                <div class="flex items-center space-x-4 mb-4">
                                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                                        <span class="text-2xl text-white font-bold">
                                            <?php echo e(substr($member->name, 0, 1)); ?>

                                        </span>
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-xl text-green-900 dark:text-green-100">
                                            <?php echo e($member->name); ?>

                                        </h4>
                                        <p class="text-green-700 dark:text-green-300">वर्तमान सदस्य</p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div class="space-y-2">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-blue-500">👨‍👦</span>
                                            <span class="text-gray-700 dark:text-gray-300"><strong>पिता:</strong> <?php echo e($member->fathers_husband_name); ?></span>
                                        </div>
                                        <?php if($member->birth_date): ?>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-green-500">🎂</span>
                                                <span class="text-gray-700 dark:text-gray-300"><strong>जन्म:</strong> <?php echo e($member->birth_date->format('d/m/Y')); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($member->department_name): ?>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-purple-500">🏢</span>
                                                <span class="text-gray-700 dark:text-gray-300"><strong>विभाग:</strong> <?php echo e($member->department_name); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="space-y-2">
                                        <?php if($member->address): ?>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-red-500">🏠</span>
                                                <span class="text-gray-700 dark:text-gray-300"><strong>पता:</strong> <?php echo e(Str::limit($member->address, 50)); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-indigo-500">🆔</span>
                                            <span class="text-gray-700 dark:text-gray-300"><strong>सदस्यता:</strong> <?php echo e($member->membership_number); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- No Ancestors Message -->
            <div class="bg-gradient-to-br from-gray-50 to-blue-50 dark:from-navy-800 dark:to-navy-700 border border-gray-200 dark:border-navy-600 rounded-2xl shadow-xl">
                <div class="px-8 py-12 text-center">
                    <div class="text-6xl mb-4">🌳</div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">कोई पूर्वज जानकारी नहीं</h3>
                    <p class="text-lg text-gray-600 dark:text-gray-400">
                        आपके सदस्यता आवेदन में पूर्वजों की जानकारी उपलब्ध नहीं है।
                    </p>
                    <div class="mt-6">
                        <p class="text-sm text-gray-500 dark:text-gray-500">
                            कृपया अपनी प्रोफाइल में पूर्वजों की जानकारी जोड़ें।
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Children Section (Santan Vivran) -->
        <?php if($member->children && count($member->children) > 0): ?>
            <div class="mt-8">
                <div class="bg-white/80 dark:bg-navy-800/80 backdrop-blur-sm border border-white/20 dark:border-navy-700/50 rounded-2xl shadow-2xl overflow-hidden">
                    <div class="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 p-6 text-center">
                        <h3 class="text-2xl font-bold text-white mb-2">
                            👶 संतान विवरण
                        </h3>
                        <p class="text-orange-100"><?php echo e($member->name); ?> की संतान की जानकारी</p>
                    </div>

                    <div class="p-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php $__currentLoopData = $member->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="child-card bg-gradient-to-br from-orange-50 to-pink-50 dark:from-orange-900/30 dark:to-pink-900/30 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-orange-200 dark:border-orange-600 p-6">
                                    <!-- Child Avatar -->
                                    <div class="text-center mb-4">
                                        <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center mx-auto shadow-lg">
                                            <span class="text-2xl text-white font-bold">
                                                <?php echo e(substr($child['name'] ?? 'N', 0, 1)); ?>

                                            </span>
                                        </div>
                                        <div class="text-2xl mt-2">
                                            <?php echo e($child['gender'] === 'male' ? '👦' : '👧'); ?>

                                        </div>
                                    </div>

                                    <!-- Child Name -->
                                    <h4 class="font-bold text-xl text-orange-900 dark:text-orange-100 mb-3 text-center">
                                        <?php echo e($child['name'] ?? 'अज्ञात'); ?>

                                    </h4>

                                    <!-- Child Details -->
                                    <div class="space-y-3 text-sm">
                                        <div class="flex items-center justify-center space-x-2">
                                            <span class="text-pink-500 text-lg">
                                                <?php echo e($child['gender'] === 'male' ? '👦' : '👧'); ?>

                                            </span>
                                            <span class="text-gray-700 dark:text-gray-300 font-semibold">
                                                <?php echo e($child['gender'] === 'male' ? 'पुत्र' : 'पुत्री'); ?>

                                            </span>
                                        </div>

                                        <?php if(!empty($child['dob'])): ?>
                                            <div class="flex items-center justify-center space-x-2">
                                                <span class="text-green-500 text-lg">🎂</span>
                                                <span class="text-gray-700 dark:text-gray-300">
                                                    <?php echo e(\Carbon\Carbon::parse($child['dob'])->format('d/m/Y')); ?>

                                                </span>
                                            </div>
                                        <?php endif; ?>

                                        <?php if(!empty($child['education'])): ?>
                                            <div class="flex items-center justify-center space-x-2">
                                                <span class="text-blue-500 text-lg">📚</span>
                                                <span class="text-gray-700 dark:text-gray-300">
                                                    <?php echo e($child['education']); ?>

                                                </span>
                                            </div>
                                        <?php endif; ?>

                                        <?php if(!empty($child['occupation'])): ?>
                                            <div class="flex items-center justify-center space-x-2">
                                                <span class="text-purple-500 text-lg">💼</span>
                                                <span class="text-gray-700 dark:text-gray-300">
                                                    <?php echo e($child['occupation']); ?>

                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Child Number Badge -->
                                    <div class="absolute top-2 right-2">
                                        <div class="bg-gradient-to-r from-orange-500 to-pink-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                            #<?php echo e($index + 1); ?>

                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Children Summary -->
                        <div class="mt-8 text-center">
                            <div class="inline-block bg-gradient-to-r from-orange-100 to-pink-100 dark:from-orange-900/50 dark:to-pink-900/50 rounded-xl px-6 py-3 border border-orange-200 dark:border-orange-600">
                                <span class="text-orange-800 dark:text-orange-200 font-semibold">
                                    कुल संतान: <?php echo e(count($member->children)); ?>

                                    (<?php echo e(collect($member->children)->where('gender', 'male')->count()); ?> पुत्र,
                                    <?php echo e(collect($member->children)->where('gender', 'female')->count()); ?> पुत्री)
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    body {
        font-family: 'Noto Sans Devanagari', Arial, sans-serif;
    }

    /* Enhanced Tree Styles */
    .family-tree-container {
        position: relative;
    }

    .ancestor-card {
        position: relative;
        transition: all 0.3s ease;
    }

    .ancestor-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .child-card {
        position: relative;
        transition: all 0.3s ease;
    }

    .child-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .current-member-card {
        position: relative;
        animation: glow 2s ease-in-out infinite alternate;
    }

    @keyframes glow {
        from {
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
        }
        to {
            box-shadow: 0 0 30px rgba(34, 197, 94, 0.6);
        }
    }

    .connection-line {
        background: linear-gradient(to bottom, #6366f1, #8b5cf6);
        box-shadow: 0 0 10px rgba(99, 102, 241, 0.3);
    }

    /* Smooth transitions for view switching */
    .view-transition {
        transition: all 0.5s ease-in-out;
    }

    /* Enhanced hover effects */
    .ancestor-node:hover .connection-line {
        background: linear-gradient(to bottom, #f59e0b, #ef4444);
        box-shadow: 0 0 15px rgba(245, 158, 11, 0.5);
    }

    /* Responsive design improvements */
    @media (max-width: 768px) {
        .ancestor-card, .child-card {
            min-width: 250px;
            max-width: 280px;
        }

        .current-member-card {
            min-width: 300px;
            max-width: 320px;
        }
    }

    /* Print styles */
    @media print {
        .no-print {
            display: none !important;
        }

        body {
            background: white !important;
        }

        .bg-gradient-to-br,
        .bg-gradient-to-r {
            background: white !important;
            color: black !important;
        }

        .text-white {
            color: black !important;
        }

        .border {
            border: 1px solid #000 !important;
        }

        .shadow-xl,
        .shadow-2xl,
        .shadow-lg {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }

        .ancestor-card,
        .child-card,
        .current-member-card {
            break-inside: avoid;
            page-break-inside: avoid;
        }

        .connection-line {
            background: #000 !important;
        }
    }

    /* Dark mode enhancements */
    @media (prefers-color-scheme: dark) {
        .connection-line {
            box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
        }
    }
</style>

<script>
    // Toggle between tree view and list view
    function toggleTreeView() {
        const treeView = document.getElementById('treeView');
        const listView = document.getElementById('listView');
        const toggleText = document.getElementById('viewToggleText');

        if (treeView.classList.contains('hidden')) {
            // Show tree view
            treeView.classList.remove('hidden');
            listView.classList.add('hidden');
            toggleText.textContent = '📋 लिस्ट व्यू';
        } else {
            // Show list view
            treeView.classList.add('hidden');
            listView.classList.remove('hidden');
            toggleText.textContent = '🌳 ट्री व्यू';
        }
    }

    // Add smooth scroll animations
    document.addEventListener('DOMContentLoaded', function() {
        // Animate cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all cards
        document.querySelectorAll('.ancestor-card, .child-card, .current-member-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Add click effects to cards
        document.querySelectorAll('.ancestor-card, .child-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Add floating animation to current member card
        const currentMemberCard = document.querySelector('.current-member-card');
        if (currentMemberCard) {
            setInterval(() => {
                currentMemberCard.style.transform = 'translateY(-2px)';
                setTimeout(() => {
                    currentMemberCard.style.transform = 'translateY(0px)';
                }, 1000);
            }, 3000);
        }
    });

    // Enhanced print function
    function printFamilyTree() {
        // Show tree view for printing
        const treeView = document.getElementById('treeView');
        const listView = document.getElementById('listView');

        const wasTreeHidden = treeView.classList.contains('hidden');

        if (wasTreeHidden) {
            treeView.classList.remove('hidden');
            listView.classList.add('hidden');
        }

        window.print();

        // Restore original view after printing
        if (wasTreeHidden) {
            treeView.classList.add('hidden');
            listView.classList.remove('hidden');
        }
    }

    // Override the print button to use enhanced print function
    document.addEventListener('DOMContentLoaded', function() {
        const printButtons = document.querySelectorAll('[onclick="window.print()"]');
        printButtons.forEach(button => {
            button.setAttribute('onclick', 'printFamilyTree()');
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.member', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web-yadav-samaj\resources\views/member/family-tree.blade.php ENDPATH**/ ?>