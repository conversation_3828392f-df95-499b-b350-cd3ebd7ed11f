<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Services\PdfService;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth:member');
    }

    /**
     * Show the member dashboard.
     */
    public function index()
    {
        $member = Auth::guard('member')->user();
        
        return view('member.dashboard', compact('member'));
    }

    /**
     * Show member profile.
     */
    public function profile()
    {
        $member = Auth::guard('member')->user()->load(['membershipVarg', 'educationQualification', 'officeMaster', 'departmentMaster']);

        return view('member.profile', compact('member'));
    }

    /**
     * Show profile edit form.
     */
    public function editProfile()
    {
        $member = Auth::guard('member')->user();

        // Load master data for dropdowns (optional for better UX)
        $membershipVargs = \App\Models\MembershipVargMaster::active()->ordered()->get();
        $educationQualifications = \App\Models\EducationQualification::active()->ordered()->get();
        $officeMasters = \App\Models\OfficeMaster::active()->ordered()->get();
        $departmentMasters = \App\Models\DepartmentMaster::active()->ordered()->get();
        $yadavVargs = \App\Models\YadavVarg::active()->ordered()->get();
        $divisionMasters = \App\Models\DivisionMaster::active()->ordered()->get();
        $districtMasters = \App\Models\DistrictMaster::active()->ordered()->get();
        $vikaskhandMasters = \App\Models\VikaskhandMaster::active()->ordered()->get();

        return view('member.profile-edit', compact(
            'member', 'membershipVargs', 'educationQualifications', 'officeMasters',
            'departmentMasters', 'yadavVargs', 'divisionMasters', 'districtMasters', 'vikaskhandMasters'
        ));
    }

    /**
     * Update member profile - comprehensive update.
     */
    public function updateProfile(Request $request)
    {
        $member = Auth::guard('member')->user();

        // Comprehensive validation
        $validatedData = $request->validate([
            // Basic Information
            'name' => ['required', 'string', 'max:255'],
            'fathers_husband_name' => ['required', 'string', 'max:255'],
            'mobile_number' => ['required', 'string', 'size:10', 'unique:memberships,mobile_number,' . $member->id],
            'birth_date' => ['nullable', 'date', 'before:today'],
            'marital_status' => ['nullable', 'string', 'in:अविवाहित,विवाहित,विधवा,तलाकशुदा'],
            'vivah_tithi' => ['nullable', 'date'],
            'spouse_name' => ['nullable', 'string', 'max:255'],
            'spouse_education' => ['nullable', 'string', 'max:255'],
            'spouse_education_details' => ['nullable', 'string', 'max:2000'],
            'spouse_work_details' => ['nullable', 'string', 'max:2000'],
            'family_members' => ['nullable', 'integer', 'min:1', 'max:50'],
            'education' => ['nullable', 'string', 'max:255'],
            'caste_details' => ['nullable', 'string', 'max:255'],

            // Professional Information
            'department_name' => ['nullable', 'string', 'max:255'],
            'office' => ['nullable', 'string', 'max:255'],
            'address' => ['nullable', 'string', 'max:1000'],

            // Proposer Information
            'member_name_signature' => ['nullable', 'string', 'max:255'],
            'mobile' => ['nullable', 'string', 'size:10'],
            'proposer_address' => ['nullable', 'string', 'max:1000'],
            'vibhag' => ['nullable', 'string', 'max:255'],

            // Authentication
            'email' => ['required', 'string', 'email', 'unique:memberships,email,' . $member->id],
            'current_password' => ['nullable', 'string'],
            'password' => ['nullable', 'string', 'min:8', 'confirmed'],

            // File uploads
            'photo' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:2048'],
            'signature' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:1024'],

            // Children information (JSON)
            'children' => ['nullable', 'array'],
            'children.*.name' => ['required_with:children', 'string', 'max:255'],
            'children.*.gender' => ['required_with:children', 'string', 'in:पुत्र,पुत्री'],
            'children.*.dob' => ['required_with:children', 'date', 'before:today'],
            'children.*.education' => ['nullable', 'string', 'max:255'],
            'children.*.education_details' => ['nullable', 'string', 'max:2000'],
            'children.*.occupation' => ['nullable', 'string', 'max:255'],
            'children.*.marital_status' => ['nullable', 'string', 'in:अविवाहित,विवाहित'],
            'children.*.spouse_name' => ['nullable', 'string', 'max:255'],
            'children.*.spouse_occupation' => ['nullable', 'string', 'max:255'],

            // Ancestors information (JSON)
            'ancestors' => ['nullable', 'array'],
            'ancestors.*.generation_level' => ['required_with:ancestors', 'integer', 'min:1', 'max:7'],
            'ancestors.*.name' => ['required_with:ancestors', 'string', 'max:255'],
            'ancestors.*.birth_date' => ['nullable', 'date', 'before:today'],
            'ancestors.*.death_date' => ['nullable', 'date', 'before:today'],
            'ancestors.*.birth_place' => ['nullable', 'string', 'max:255'],
            'ancestors.*.occupation' => ['nullable', 'string', 'max:255'],
            'ancestors.*.spouse' => ['nullable', 'string', 'max:255'],
            'ancestors.*.children_count' => ['nullable', 'integer', 'min:0', 'max:50'],
            'ancestors.*.children_details' => ['nullable', 'string', 'max:2000'],
            'ancestors.*.gotra' => ['nullable', 'string', 'max:255'],
        ]);

        // Handle file uploads
        if ($request->hasFile('photo')) {
            // Delete old photo if exists
            if ($member->photo && \Storage::exists($member->photo)) {
                \Storage::delete($member->photo);
            }
            $validatedData['photo'] = $request->file('photo')->store('member-photos', 'public');
        }

        if ($request->hasFile('signature')) {
            // Delete old signature if exists
            if ($member->signature && \Storage::exists($member->signature)) {
                \Storage::delete($member->signature);
            }
            $validatedData['signature'] = $request->file('signature')->store('member-signatures', 'public');
        }

        // Handle password update
        if ($request->filled('password')) {
            if (!$request->filled('current_password') ||
                !Hash::check($request->current_password, $member->password)) {
                return back()->withErrors(['current_password' => 'वर्तमान पासवर्ड गलत है।']);
            }
            $validatedData['password'] = $request->password;
        } else {
            // Remove password from update data if not changing
            unset($validatedData['password'], $validatedData['current_password']);
        }

        // Remove confirmation field
        unset($validatedData['password_confirmation']);

        $member->update($validatedData);

        return back()->with('success', 'प्रोफाइल सफलतापूर्वक अपडेट हो गया।');
    }

    /**
     * Download membership card as PDF using mPDF.
     */
    public function downloadCard()
    {
        $member = Auth::guard('member')->user();

        if (!$member->isApproved()) {
            return back()->with('error', 'सदस्यता कार्ड केवल स्वीकृत सदस्यों के लिए उपलब्ध है।');
        }

        try {
            $pdfService = new PdfService();
            return $pdfService->downloadMembershipCard($member);

        } catch (\Exception $e) {
            \Log::error('mPDF Generation Error: ' . $e->getMessage());
            \Log::error('mPDF Generation Stack Trace: ' . $e->getTraceAsString());

            return back()->with('error', 'PDF जेनरेट करने में त्रुटि हुई। Error: ' . $e->getMessage());
        }
    }


    /**
     * Preview membership card in browser using mPDF template.
     */
    public function previewCard()
    {
        $member = Auth::guard('member')->user();

        if (!$member->isApproved()) {
            return back()->with('error', 'सदस्यता कार्ड केवल स्वीकृत सदस्यों के लिए उपलब्ध है।');
        }

        try {
            $pdfService = new PdfService();
            $html = $pdfService->generateHTML($member);
            return response($html)->header('Content-Type', 'text/html; charset=utf-8');
        } catch (\Exception $e) {
            return back()->with('error', 'प्रीव्यू जेनरेट करने में त्रुटि हुई।');
        }
    }

    /**
     * Show family tree page
     */
    public function familyTree()
    {
        $member = Auth::guard('member')->user();
        return view('member.family-tree', compact('member'));
    }



    /**
     * Show application status.
     */
    public function applicationStatus()
    {
        $member = Auth::guard('member')->user();
        
        return view('member.application-status', compact('member'));
    }
}
